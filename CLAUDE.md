# CLAUDE.md - 开发指导文档

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

> 📖 **项目概述请参考**: [README.md](./README.md)

## 🏗️ 核心架构指导

### 前端矩阵引擎

- **MatrixCore.ts**: 具有可配置业务模式的中央处理引擎
- **MatrixStore.ts**: 基于 Zustand 的状态管理，支持持久化和计算属性
- **MatrixTypes.ts**: 支持数据驱动渲染的统一类型系统
- **33x33 网格**: 1089 个单元格同时渲染，无需虚拟化

### 业务模式系统

系统支持 4 种不同的渲染模式：

- **坐标模式**: 在每个单元格中显示 x,y 坐标
- **颜色模式**: 显示 9 种颜色类型（包括黑色）的颜色数据
- **等级模式**: 显示数据等级（1-4级）
- **词语模式**: 在单元格中显示文本内容

### 词库管理系统

- **29个词库**: 9种颜色 × 4个级别 - 1个（黑色4级不存在）
- **重复检测**: 同词库内阻止重复，跨词库提醒重复
- **高亮系统**: 跨词库重复词语自动分配高亮颜色
- **全局索引**: 基于Map的高性能词语索引系统

## 🚀 快速开发指导

> 📖 **完整安装指南请参考**: [README.md](./README.md)

### 快速启动

```bash
# 1. 安装依赖
pnpm install

# 2. 启动开发环境
pnpm run dev              # 同时启动前后端

# 3. 访问应用
# 前端: http://localhost:4096
# 后端API: http://localhost:8000/docs
```

### 开发工作流

```bash
# 代码质量检查
pnpm run lint             # ESLint检查
pnpm run type-check       # TypeScript类型检查
pnpm run format           # Prettier格式化

# 测试
pnpm run test:frontend    # 前端单元测试
pnpm run test:e2e         # E2E测试
```

## 关键技术细节

### 状态管理模式

- **单一数据源**: 所有矩阵状态通过 Zustand store 管理
- **计算属性**: 缓存计算以提高性能优化
- **持久化**: 自动 LocalStorage 备份，支持 Map/Set 序列化
- **性能**: 与 useState 方法相比减少 97% 的重渲染

### 矩阵数据结构

```typescript
interface MatrixData {
  cells: Map<string, CellData>;        // key: "x,y"
  selectedCells: Set<string>;          // 选中的单元格键
  hoveredCell: string | null;          // 当前悬停的单元格键
  focusedCell: string | null;          // 当前聚焦的单元格键
}
```

### 性能优化

- **React.memo**: 应用于 15 个组件以优化渲染
- **useMemo/useCallback**: 广泛缓存计算和事件处理器
- **批量更新**: 通过 Immer 进行高效的批量单元格更新
- **预建索引**: A 组数据中的优化坐标查找

### 后端 API 结构

- **FastAPI 框架**: 自动生成的 API 文档
- **模块化架构**: 不同业务功能的独立模块
- **错误处理**: 具有自定义错误类型的全面异常处理
- **中间件**: 请求计时、日志记录和 CORS 支持

## 📁 关键文件结构

### 前端核心文件

```text
apps/frontend/
├── core/matrix/           # 🎯 矩阵引擎核心
│   ├── MatrixCore.ts     # 数据处理引擎
│   ├── MatrixStore.ts    # Zustand状态管理
│   └── MatrixTypes.ts    # 类型定义系统
├── core/wordLibrary/     # 📚 词库管理系统
│   ├── WordLibraryCore.ts    # 词库核心逻辑
│   └── WordLibraryStore.ts   # 词库状态管理
├── components/           # 🧩 React组件
│   ├── Matrix.tsx        # 主矩阵组件
│   ├── Controls.tsx      # 控制面板
│   └── ui/              # UI基础组件
└── app/                 # 📄 Next.js页面
```

### 后端核心文件

```text
apps/backend/
├── app/main.py          # 🚀 FastAPI应用入口
├── app/api/v1/          # 🔌 API路由
└── app/core/            # ⚙️ 核心工具
```

## 💻 开发指导原则

### 🎯 核心开发规范

- **状态管理**: 仅使用 Zustand，避免 useState 管理矩阵数据
- **性能优先**: 1089个单元格同时渲染，所有操作需优化
- **类型安全**: TypeScript 严格模式，100% 类型覆盖
- **组件设计**: 函数式组件 + React.memo 优化

### 🔧 关键开发模式

#### 矩阵数据处理

```typescript
// ✅ 正确：使用 MatrixStore
const { updateCell, batchUpdate } = useMatrixStore();

// ❌ 错误：使用 useState 管理矩阵数据
const [matrixData, setMatrixData] = useState();
```

#### 词库管理

```typescript
// ✅ 正确：使用词库验证系统
const validation = validateInput(libraryKey, text);
const crossCheck = checkCrossLibraryDuplicate(text);

// ❌ 错误：直接操作词库数据
library.words.push(newWord);
```

### 🧪 测试指导

- **单元测试**: 专注于核心逻辑，避免UI测试
- **E2E测试**: 关键用户流程，矩阵交互
- **性能测试**: 1089个单元格渲染性能

### ⚠️ 重要注意事项

- **矩阵性能**: 所有操作需考虑1089个单元格的性能影响
- **词库系统**: 29个词库的重复检测和高亮管理
- **状态同步**: LocalStorage 持久化与实时更新
- **类型定义**: 所有新功能必须有完整的TypeScript类型
